<template>
  <div class="ccxt-demo">
    <h2>CCXT 加密货币价格演示</h2>
    
    <div class="controls">
      <select v-model="selectedExchange" @change="onExchangeChange">
        <option value="">选择交易所</option>
        <option v-for="exchange in availableExchanges" :key="exchange" :value="exchange">
          {{ exchange }}
        </option>
      </select>
      
      <input 
        v-model="symbol" 
        placeholder="输入交易对 (例如: BTC/USDT)"
        @keyup.enter="fetchPrice"
      />
      
      <button @click="fetchPrice" :disabled="loading || !selectedExchange || !symbol">
        {{ loading ? '获取中...' : '获取价格' }}
      </button>
    </div>

    <div v-if="error" class="error">
      错误: {{ error }}
    </div>

    <div v-if="ticker" class="ticker-info">
      <h3>{{ symbol }} 价格信息</h3>
      <div class="price-grid">
        <div class="price-item">
          <label>交易所:</label>
          <span>{{ selectedExchange }}</span>
        </div>
        <div class="price-item">
          <label>当前价格:</label>
          <span class="price">${{ ticker.last?.toFixed(2) || 'N/A' }}</span>
        </div>
        <div class="price-item">
          <label>买入价:</label>
          <span>${{ ticker.bid?.toFixed(2) || 'N/A' }}</span>
        </div>
        <div class="price-item">
          <label>卖出价:</label>
          <span>${{ ticker.ask?.toFixed(2) || 'N/A' }}</span>
        </div>
        <div class="price-item">
          <label>24h 最高:</label>
          <span>${{ ticker.high?.toFixed(2) || 'N/A' }}</span>
        </div>
        <div class="price-item">
          <label>24h 最低:</label>
          <span>${{ ticker.low?.toFixed(2) || 'N/A' }}</span>
        </div>
        <div class="price-item">
          <label>24h 成交量:</label>
          <span>{{ ticker.baseVolume?.toFixed(2) || 'N/A' }}</span>
        </div>
        <div class="price-item">
          <label>24h 变化:</label>
          <span :class="{ 'positive': ticker.percentage > 0, 'negative': ticker.percentage < 0 }">
            {{ ticker.percentage?.toFixed(2) || 'N/A' }}%
          </span>
        </div>
      </div>
    </div>

    <div class="info">
      <h3>使用说明</h3>
      <ul>
        <li>选择一个支持的交易所</li>
        <li>输入交易对符号，例如: BTC/USDT, ETH/USDT, BNB/USDT</li>
        <li>点击"获取价格"按钮或按回车键</li>
        <li>查看实时价格信息</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ccxt from 'ccxt'

// 响应式数据
const selectedExchange = ref('')
const symbol = ref('BTC/USDT')
const ticker = ref(null)
const loading = ref(false)
const error = ref('')
const availableExchanges = ref([])

// 获取可用的交易所列表
onMounted(() => {
  // 选择一些常用且稳定的交易所
  const popularExchanges = [
    'binance',
    'coinbase',
    'kraken',
    'bitfinex',
    'huobi',
    'okx',
    'bybit'
  ]
  
  // 过滤出实际可用的交易所
  availableExchanges.value = popularExchanges.filter(exchangeId => {
    try {
      return ccxt[exchangeId] !== undefined
    } catch (e) {
      return false
    }
  })
  
  // 默认选择 binance
  if (availableExchanges.value.includes('binance')) {
    selectedExchange.value = 'binance'
  }
})

// 交易所变化处理
const onExchangeChange = () => {
  ticker.value = null
  error.value = ''
}

// 获取价格
const fetchPrice = async () => {
  if (!selectedExchange.value || !symbol.value) {
    error.value = '请选择交易所和输入交易对'
    return
  }

  loading.value = true
  error.value = ''
  ticker.value = null

  try {
    // 创建交易所实例
    const ExchangeClass = ccxt[selectedExchange.value]
    const exchange = new ExchangeClass({
      sandbox: false, // 使用真实环境
      enableRateLimit: true, // 启用速率限制
    })

    // 获取ticker数据
    const tickerData = await exchange.fetchTicker(symbol.value)
    ticker.value = tickerData

  } catch (e) {
    console.error('获取价格失败:', e)
    error.value = e.message || '获取价格失败，请检查交易对是否正确'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.ccxt-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.controls select,
.controls input,
.controls button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.controls button {
  background-color: #42b883;
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.controls button:hover:not(:disabled) {
  background-color: #369870;
}

.controls button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error {
  background-color: #fee;
  color: #c33;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.ticker-info {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.price-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 15px;
}

.price-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #eee;
}

.price-item label {
  font-weight: bold;
  color: #666;
}

.price {
  font-size: 1.2em;
  font-weight: bold;
  color: #42b883;
}

.positive {
  color: #4caf50;
}

.negative {
  color: #f44336;
}

.info {
  background-color: #f0f8ff;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #42b883;
}

.info h3 {
  margin-top: 0;
  color: #333;
}

.info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info li {
  margin-bottom: 5px;
  color: #666;
}

@media (max-width: 600px) {
  .controls {
    flex-direction: column;
  }
  
  .price-grid {
    grid-template-columns: 1fr;
  }
}
</style>
